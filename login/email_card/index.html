<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置邮箱</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="email-config-card">
        <h2>配置邮箱</h2>
        <p>选择邮箱类型</p>
        
        <!-- 邮箱类型下拉选择 -->
        <div class="form-group">
            <select id="email-provider" class="email-provider-select">
                <option value="qq">QQ邮箱</option>
                <option value="163">163邮箱</option>
                <option value="126">126邮箱</option>
                <option value="gmail">Gmail</option>
                <option value="outlook">Outlook</option>
            </select>
        </div>

        <!-- 配置指导区域 -->
        <div class="guidance" id="provider-guidance">
            <p id="guidance-title">📧 QQ邮箱配置指导</p>
            <ol id="guidance-steps">
                <li>登录网页端 → 右上角 设置 → 账户</li>
                <li>找到「POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV 服务」，点击 开启 IMAP/SMTP</li>
                <li>系统会弹出短信二次验证，验证后生成一次性 16 位授权码，复制保存，填到下方授权码处</li>
            </ol>
            <a href="https://mail.qq.com" target="_blank" class="auth-code-link" id="guidance-link">点击进入QQ邮箱设置</a>
        </div>

        <!-- 表单配置 -->
        <form>
            <div class="form-group">
                <label for="email-address">邮箱地址</label>
                <input type="email" id="email-address" placeholder="例如: <EMAIL>">
            </div>
            <div class="form-group">
                <label for="auth-code" id="auth-code-label">授权码</label>
                <input type="password" id="auth-code" placeholder="请输入QQ邮箱授权码">
            </div>
            
            <!-- 自动配置参数 -->
            <div class="auto-config">
                <p>自动配置参数</p>
                <div class="config-item">
                    <span>SMTP服务器</span>
                    <span id="smtp-server">smtp.qq.com</span>
                </div>
                <div class="config-item">
                    <span>SMTP端口</span>
                    <span id="smtp-port">465 (SSL)</span>
                </div>
                <div class="config-item">
                    <span>IMAP服务器</span>
                    <span id="imap-server">imap.qq.com</span>
                </div>
                <div class="config-item">
                    <span>IMAP端口</span>
                    <span id="imap-port">993 (SSL)</span>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn-test">测试连接</button>
                <button type="submit" class="btn-save">保存配置</button>
            </div>
        </form>
    </div>
    
    <script src="script.js"></script>
</body>
</html> 