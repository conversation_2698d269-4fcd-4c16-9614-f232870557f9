<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置邮箱</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="email-config-card">
        <h2>配置邮箱</h2>
        <p>选择邮箱类型</p>
        <div class="email-provider">
            <span>QQ邮箱</span>
        </div>
        <div class="guidance">
            <p>📧 QQ邮箱配置指导</p>
            <ol>
                <li>登录网页端 → 右上角 设置 → 账户</li>
                <li>找到「POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV 服务」，点击 开启 IMAP/SMTP</li>
                <li>系统会弹出短信二次验证，验证后生成一次性 16 位授权码，复制保存，填到下方授权码处</li>
            </ol>
            <a href="#" class="auth-code-link">点击获取授权码</a>
        </div>
        <form>
            <div class="form-group">
                <label for="email-address">邮箱地址</label>
                <input type="email" id="email-address" placeholder="例如: <EMAIL>">
            </div>
            <div class="form-group">
                <label for="auth-code">授权码</label>
                <input type="password" id="auth-code" placeholder="请输入QQ邮箱授权码">
            </div>
            <div class="auto-config">
                <p>自动配置参数</p>
                <div class="config-item">
                    <span>SMTP服务器</span>
                    <span>smtp.qq.com</span>
                </div>
                <div class="config-item">
                    <span>SMTP端口</span>
                    <span>465 (SSL)</span>
                </div>
                <div class="config-item">
                    <span>IMAP服务器</span>
                    <span>imap.qq.com</span>
                </div>
                <div class="config-item">
                    <span>IMAP端口</span>
                    <span>993 (SSL)</span>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-test">测试连接</button>
                <button type="submit" class="btn-save">保存配置</button>
            </div>
        </form>
    </div>
</body>
</html> 