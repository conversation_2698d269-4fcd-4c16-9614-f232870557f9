// 邮箱配置切换功能
class EmailConfig {
    constructor() {
        this.currentProvider = 'qq';
        this.providerConfigs = {
            qq: {
                title: '📧 QQ邮箱配置指导',
                steps: [
                    '登录网页端 → 右上角 设置 → 账户',
                    '找到「POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV 服务」，点击 开启 IMAP/SMTP',
                    '系统会弹出短信二次验证，验证后生成一次性 16 位授权码，复制保存，填到下方授权码处'
                ],
                link: 'https://mail.qq.com',
                linkText: '点击进入QQ邮箱设置',
                emailPlaceholder: '例如: <EMAIL>',
                authLabel: '授权码',
                authPlaceholder: '请输入QQ邮箱授权码',
                smtp: { server: 'smtp.qq.com', port: '465 (SSL)' },
                imap: { server: 'imap.qq.com', port: '993 (SSL)' }
            },
            163: {
                title: '📧 163邮箱配置指导',
                steps: [
                    '登录网页端 → 右上角 设置 → POP3/SMTP/IMAP',
                    '开启 IMAP/SMTP 服务',
                    '设置客户端授权密码（不是登录密码）'
                ],
                link: 'https://mail.163.com',
                linkText: '点击进入163邮箱设置',
                emailPlaceholder: '例如: <EMAIL>',
                authLabel: '授权码',
                authPlaceholder: '请输入163邮箱授权码',
                smtp: { server: 'smtp.163.com', port: '465 (SSL)' },
                imap: { server: 'imap.163.com', port: '993 (SSL)' }
            },
            126: {
                title: '📧 126邮箱配置指导',
                steps: [
                    '登录网页端 → 右上角 设置 → POP3/SMTP/IMAP',
                    '开启 IMAP/SMTP 服务',
                    '设置客户端授权密码'
                ],
                link: 'https://mail.126.com',
                linkText: '点击进入126邮箱设置',
                emailPlaceholder: '例如: <EMAIL>',
                authLabel: '授权码',
                authPlaceholder: '请输入126邮箱授权码',
                smtp: { server: 'smtp.126.com', port: '465 (SSL)' },
                imap: { server: 'imap.126.com', port: '993 (SSL)' }
            },
            gmail: {
                title: '📧 Gmail配置指导',
                steps: [
                    '进入Google账户设置 → 安全性',
                    '开启两步验证',
                    '生成应用专用密码'
                ],
                link: 'https://myaccount.google.com/security',
                linkText: '点击进入Google安全设置',
                emailPlaceholder: '例如: <EMAIL>',
                authLabel: '应用专用密码',
                authPlaceholder: '请输入Gmail应用专用密码',
                smtp: { server: 'smtp.gmail.com', port: '587 (TLS)' },
                imap: { server: 'imap.gmail.com', port: '993 (SSL)' }
            },
            outlook: {
                title: '📧 Outlook配置指导',
                steps: [
                    '登录 outlook.live.com → 设置 → 查看所有Outlook设置',
                    '邮件 → 同步电子邮件',
                    '启用POP和IMAP'
                ],
                link: 'https://outlook.live.com',
                linkText: '点击进入Outlook设置',
                emailPlaceholder: '例如: <EMAIL>',
                authLabel: '密码',
                authPlaceholder: '请输入Outlook密码',
                smtp: { server: 'smtp-mail.outlook.com', port: '587 (TLS)' },
                imap: { server: 'outlook.office365.com', port: '993 (SSL)' }
            }
        };
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateConfig(this.currentProvider);
    }

    bindEvents() {
        // 邮箱提供商下拉菜单切换
        const providerSelect = document.getElementById('email-provider');
        providerSelect.addEventListener('change', (e) => {
            this.switchProvider(e.target.value);
        });

        // 测试连接按钮
        const testButton = document.querySelector('.btn-test');
        testButton.addEventListener('click', (e) => {
            e.preventDefault();
            this.testConnection();
        });

        // 保存配置按钮
        const saveButton = document.querySelector('.btn-save');
        saveButton.addEventListener('click', (e) => {
            e.preventDefault();
            this.saveConfig();
        });
    }

    switchProvider(providerType) {
        this.currentProvider = providerType;
        this.updateConfig(providerType);
        
        // 清空表单
        document.getElementById('email-address').value = '';
        document.getElementById('auth-code').value = '';
    }

    updateConfig(providerType) {
        const config = this.providerConfigs[providerType];
        if (!config) return;

        // 更新指导内容
        document.getElementById('guidance-title').textContent = config.title;
        
        const stepsList = document.getElementById('guidance-steps');
        stepsList.innerHTML = '';
        config.steps.forEach(step => {
            const li = document.createElement('li');
            li.textContent = step;
            stepsList.appendChild(li);
        });
        
        const guidanceLink = document.getElementById('guidance-link');
        guidanceLink.href = config.link;
        guidanceLink.textContent = config.linkText;

        // 更新表单
        document.getElementById('email-address').placeholder = config.emailPlaceholder;
        document.getElementById('auth-code-label').textContent = config.authLabel;
        document.getElementById('auth-code').placeholder = config.authPlaceholder;

        // 更新服务器配置
        document.getElementById('smtp-server').textContent = config.smtp.server;
        document.getElementById('smtp-port').textContent = config.smtp.port;
        document.getElementById('imap-server').textContent = config.imap.server;
        document.getElementById('imap-port').textContent = config.imap.port;
    }

    validateForm() {
        const emailInput = document.getElementById('email-address');
        const authInput = document.getElementById('auth-code');

        let isValid = true;
        let errorMessage = '';

        // 验证邮箱
        if (!emailInput.value.trim()) {
            errorMessage = '请输入邮箱地址';
            isValid = false;
        } else if (!this.isValidEmail(emailInput.value)) {
            errorMessage = '请输入有效的邮箱地址';
            isValid = false;
        }

        // 验证密码/授权码
        if (!authInput.value.trim()) {
            const label = document.getElementById('auth-code-label').textContent;
            errorMessage = `请输入${label}`;
            isValid = false;
        }

        if (!isValid) {
            this.showMessage(errorMessage, 'error');
        }

        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    testConnection() {
        if (!this.validateForm()) return;

        const testButton = document.querySelector('.btn-test');
        const originalText = testButton.textContent;
        
        // 显示加载状态
        testButton.textContent = '测试中...';
        testButton.disabled = true;

        // 模拟测试连接
        setTimeout(() => {
            testButton.textContent = originalText;
            testButton.disabled = false;
            
            // 模拟测试结果
            const success = Math.random() > 0.3; // 70%成功率
            if (success) {
                this.showMessage('连接测试成功！', 'success');
            } else {
                this.showMessage('连接测试失败，请检查配置信息', 'error');
            }
        }, 2000);
    }

    saveConfig() {
        if (!this.validateForm()) return;

        const saveButton = document.querySelector('.btn-save');
        const originalText = saveButton.textContent;
        
        // 显示加载状态
        saveButton.textContent = '保存中...';
        saveButton.disabled = true;

        // 获取表单数据
        const formData = this.getFormData();
        
        // 模拟保存配置
        setTimeout(() => {
            saveButton.textContent = originalText;
            saveButton.disabled = false;
            
            // 保存到本地存储
            localStorage.setItem('emailConfig', JSON.stringify({
                provider: this.currentProvider,
                data: formData,
                timestamp: new Date().toISOString()
            }));
            
            this.showMessage('配置保存成功！', 'success');
        }, 1500);
    }

    getFormData() {
        return {
            emailAddress: document.getElementById('email-address').value,
            authCode: document.getElementById('auth-code').value,
            smtpServer: document.getElementById('smtp-server').textContent,
            smtpPort: document.getElementById('smtp-port').textContent,
            imapServer: document.getElementById('imap-server').textContent,
            imapPort: document.getElementById('imap-port').textContent
        };
    }

    showMessage(message, type = 'info') {
        // 移除现有消息
        const existingMessage = document.querySelector('.message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;

        // 插入到卡片顶部
        const card = document.querySelector('.email-config-card');
        card.insertBefore(messageEl, card.firstChild);

        // 自动移除消息
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 3000);
    }

    // 加载保存的配置
    loadSavedConfig() {
        const saved = localStorage.getItem('emailConfig');
        if (saved) {
            try {
                const config = JSON.parse(saved);
                
                // 设置下拉菜单值
                document.getElementById('email-provider').value = config.provider;
                this.switchProvider(config.provider);
                
                // 填充表单数据
                if (config.data) {
                    document.getElementById('email-address').value = config.data.emailAddress || '';
                    document.getElementById('auth-code').value = config.data.authCode || '';
                }
            } catch (e) {
                console.error('加载配置失败:', e);
            }
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    const emailConfig = new EmailConfig();
    emailConfig.loadSavedConfig();
}); 