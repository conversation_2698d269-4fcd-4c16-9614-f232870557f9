/* CSS变量定义 - 深色主题系统 */
:root {
    /* 深色主题颜色 */
    --primary-color: #6366f1;
    --primary-hover: #5b5bf6;
    --primary-alpha-15: rgba(99, 102, 241, 0.15);
    --primary-alpha-20: rgba(99, 102, 241, 0.2);
    
    /* 深色文本颜色 */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-inverse: #ffffff;
    
    /* 深色背景颜色 */
    --background-color: #0f172a;
    --background-alt: #1e293b;
    --surface-color: #334155;
    --surface-hover: #475569;
    --surface-elevated: #475569;
    --modal-bg: #1e293b;
    --modal-overlay: rgba(15, 23, 42, 0.8);
    
    /* 深色边框颜色 */
    --border-color: #475569;
    --border-light: #334155;
    --border-hover: #64748b;
    
    /* 链接颜色 */
    --link: #6366f1;
    --link-hover: #5b5bf6;
    
    /* 阴影 */
    --black-alpha-10: rgba(0, 0, 0, 0.3);
    --black-alpha-20: rgba(0, 0, 0, 0.5);
    
    /* 圆角 */
    --radius-base: 8px;
    --radius-lg: 12px;
    
    /* 动画 */
    --animation-duration: 0.2s;
    --animation-easing: ease-in-out;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: var(--modal-overlay);
    font-family: var(--font-family);
    color: var(--text-primary);
    line-height: 1.6;
    padding: 20px;
}

.email-config-card {
    background-color: var(--modal-bg);
    padding: 32px;
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 750px;
    border: 1px solid var(--border-color);
    position: relative;
}

/* 关闭按钮 */
.close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all var(--animation-duration) var(--animation-easing);
    font-size: 20px;
    line-height: 1;
}

.close-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.email-config-card h2 {
    font-size: 20px;
    color: var(--text-primary);
    margin-top: 0;
    margin-bottom: 24px;
    font-weight: 600;
}

.email-config-card > p {
    color: var(--text-secondary);
    margin-bottom: 16px;
    font-size: 14px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 14px;
}

.email-provider-select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-base);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 14px;
    font-family: var(--font-family);
    transition: all var(--animation-duration) var(--animation-easing);
    cursor: pointer;
}

.email-provider-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-15);
}

.email-provider-select:hover {
    border-color: var(--border-hover);
}

.email-provider-select option {
    background-color: var(--surface-color);
    color: var(--text-primary);
}

.guidance {
    background-color: var(--background-alt);
    padding: 16px;
    border-radius: var(--radius-base);
    margin-bottom: 24px;
    font-size: 14px;
    border: 1px solid var(--border-light);
}

.guidance p {
    margin-top: 0;
    margin-bottom: 12px;
    color: var(--text-primary);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.guidance p::before {
    content: "ℹ️";
    font-size: 16px;
}

.guidance ol {
    padding-left: 20px;
    margin-bottom: 16px;
    color: var(--text-secondary);
}

.guidance li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.auth-code-link {
    color: var(--text-inverse);
    background-color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: var(--radius-base);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all var(--animation-duration) var(--animation-easing);
}

.auth-code-link::before {
    content: "🔗";
    font-size: 14px;
}

.auth-code-link:hover {
    background-color: var(--primary-hover);
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-base);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 14px;
    font-family: var(--font-family);
    transition: all var(--animation-duration) var(--animation-easing);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-15);
}

.form-group input::placeholder {
    color: var(--text-tertiary);
}

.auto-config {
    margin-top: 24px;
    margin-bottom: 24px;
    border-top: 1px solid var(--border-light);
    padding-top: 16px;
}

.auto-config p {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    font-size: 14px;
}

.config-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-group label {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.config-group .config-value {
    background-color: var(--surface-color);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-base);
    padding: 8px 12px;
    color: var(--text-primary);
    font-size: 14px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 32px;
    justify-content: flex-end;
}

.form-actions button {
    padding: 10px 20px;
    border-radius: var(--radius-base);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--animation-duration) var(--animation-easing);
    border: 1px solid transparent;
    font-family: var(--font-family);
    min-width: 100px;
}

.btn-test {
    background-color: var(--surface-elevated);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-test:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

.btn-save {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

.btn-save:hover {
    background-color: var(--primary-hover);
    box-shadow: 0 4px 12px var(--primary-alpha-20);
    transform: translateY(-1px);
}

.btn-save:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 600px) {
    body {
        padding: 16px;
    }
    
    .email-config-card {
        padding: 24px;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions button {
        width: 100%;
    }
}

/* 消息提示样式 */
.message {
    padding: 12px 16px;
    border-radius: var(--radius-base);
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    border-left: 4px solid;
    animation: slideIn 0.3s ease-out;
}

.message-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #4ade80;
    border-color: #22c55e;
}

.message-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #f87171;
    border-color: #ef4444;
}

.message-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #60a5fa;
    border-color: #3b82f6;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 禁用状态按钮样式 */
.btn-test:disabled,
.btn-save:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-save:disabled:hover {
    background-color: var(--primary-color);
    box-shadow: none;
} 