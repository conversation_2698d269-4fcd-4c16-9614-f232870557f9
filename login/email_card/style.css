/* CSS变量定义 - 独立主题系统 */
:root {
    /* 主色调 */
    --primary-color: #1976d2;
    --primary-hover: #1565c0;
    --primary-alpha-15: rgba(25, 118, 210, 0.15);
    --primary-alpha-20: rgba(25, 118, 210, 0.2);
    
    /* 文本颜色 */
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-tertiary: #9e9e9e;
    --text-inverse: #ffffff;
    
    /* 背景颜色 */
    --background-color: #f5f5f5;
    --background-alt: #f8f9fa;
    --surface-color: #ffffff;
    --surface-hover: #f5f5f5;
    --surface-elevated: #fafafa;
    
    /* 边框颜色 */
    --border-color: #e0e0e0;
    --border-light: #eeeeee;
    --border-hover: #bdbdbd;
    
    /* 链接颜色 */
    --link: #1976d2;
    --link-hover: #1565c0;
    
    /* 阴影 */
    --black-alpha-10: rgba(0, 0, 0, 0.1);
    
    /* 圆角 */
    --radius-base: 6px;
    --radius-lg: 12px;
    
    /* 动画 */
    --animation-duration: 0.2s;
    --animation-easing: ease-in-out;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: var(--background-color);
    font-family: var(--font-family);
    color: var(--text-primary);
    line-height: 1.6;
}

.email-config-card {
    background-color: var(--surface-color);
    padding: 32px;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 12px var(--black-alpha-10);
    width: 100%;
    max-width: 500px;
    border: 1px solid var(--border-color);
}

.email-config-card h2 {
    font-size: 24px;
    color: var(--text-primary);
    margin-top: 0;
    margin-bottom: 16px;
    text-align: center;
    font-weight: 600;
}

.email-config-card > p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    text-align: center;
    font-size: 16px;
}

.email-provider-select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-base);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 16px;
    font-family: var(--font-family);
    transition: all var(--animation-duration) var(--animation-easing);
    cursor: pointer;
}

.email-provider-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-15);
}

.email-provider-select:hover {
    border-color: var(--border-hover);
}

.guidance {
    background-color: var(--background-alt);
    padding: 16px;
    border-radius: var(--radius-base);
    margin-bottom: 24px;
    font-size: 14px;
    border: 1px solid var(--border-light);
}

.guidance p {
    margin-top: 0;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-weight: bold;
}

.guidance ol {
    padding-left: 20px;
    margin-bottom: 12px;
    color: var(--text-tertiary);
}

.guidance li {
    margin-bottom: 6px;
    line-height: 1.5;
}

.auth-code-link {
    color: var(--link);
    text-decoration: none;
    font-weight: 500;
}

.auth-code-link:hover {
    color: var(--link-hover);
    text-decoration: underline;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-base);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 16px;
    font-family: var(--font-family);
    transition: all var(--animation-duration) var(--animation-easing);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-15);
}

.form-group input::placeholder {
    color: var(--text-tertiary);
}

.auto-config {
    margin-top: 24px;
    margin-bottom: 24px;
    border-top: 1px solid var(--border-light);
    padding-top: 16px;
}

.auto-config p {
    font-weight: bold;
    color: var(--text-secondary);
    margin-bottom: 12px;
    font-size: 16px;
}

.config-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    font-size: 14px;
}

.config-item span:first-child {
    color: var(--text-tertiary);
}

.config-item span:last-child {
    color: var(--text-primary);
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 16px;
    margin-top: 24px;
}

.form-actions button {
    flex-grow: 1;
    padding: 12px;
    border-radius: var(--radius-base);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--animation-duration) var(--animation-easing);
    border: 1px solid transparent;
    font-family: var(--font-family);
}

.btn-test {
    background-color: var(--surface-elevated);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-test:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

.btn-save {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

.btn-save:hover {
    background-color: var(--primary-hover);
    box-shadow: 0 2px 8px var(--primary-alpha-20);
    transform: translateY(-1px);
}

.btn-save:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 600px) {
    .email-config-card {
        margin: 16px;
        padding: 24px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions button {
        width: 100%;
    }
}

/* 消息提示样式 */
.message {
    padding: 12px 16px;
    border-radius: var(--radius-base);
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    border-left: 4px solid;
    animation: slideIn 0.3s ease-out;
}

.message-success {
    background-color: #e8f5e8;
    color: #2e7d32;
    border-color: #4caf50;
}

.message-error {
    background-color: #ffebee;
    color: #c62828;
    border-color: #f44336;
}

.message-info {
    background-color: #e3f2fd;
    color: #1565c0;
    border-color: #2196f3;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 禁用状态按钮样式 */
.btn-test:disabled,
.btn-save:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-save:disabled:hover {
    background-color: var(--primary-color);
    box-shadow: none;
} 