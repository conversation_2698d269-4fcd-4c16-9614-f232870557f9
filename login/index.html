<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 跨境运营助手</title>
    <!-- 资源预加载优化 -->
    <link rel="preconnect" href="https://cdn.staticfile.net">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://cdn.staticfile.net">
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
    
    <!-- 核心CSS框架 - 优化加载顺序 -->
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous"></noscript>
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css" crossorigin="anonymous">
    
    <!-- 项目基础样式 (注意路径调整) -->
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="../style-enhanced.css">
    <link rel="stylesheet" href="../performance-enhanced.css">
    
    <!-- 主题系统样式 (注意路径调整) -->
    <link rel="stylesheet" href="../themes/theme-switcher.css">
    <link rel="stylesheet" href="../themes/alien-effects.css">
    <link rel="stylesheet" href="../themes/dark-theme-gradient.css">
    <link rel="stylesheet" href="../themes/glassmorphism-unified.css">

    <!-- Remix Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" media="print" onload="this.media='all'">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>📧</text></svg>">
    
    <!-- 自定义补丁样式 -->
    <style>
    /* 兼容不支持 flex-gap 的浏览器 */
    .input-icon + .input-divider {
        margin-left: 12px;
    }
    .input-divider + .login-input-new {
        margin-left: 12px;
    }
    #verification-input-new {
        flex: 1 1 auto !important;
        min-width: 0 !important;
    }
    /* 确保页面在加载时可见 */
    #login-page, #register-page {
        display: block;
    }

    /* 登录方式切换按钮样式 */
    .login-mode-switch {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
    }

    .login-mode-switch-btn {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.6);
        font-size: 13px;
        font-weight: 400;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .login-mode-switch-btn:hover {
        color: rgba(255, 255, 255, 0.8);
        background: rgba(255, 255, 255, 0.05);
    }

    .login-mode-switch-btn i {
        font-size: 12px;
    }

    /* 忘记密码链接样式 */
    .forgot-password-link:hover {
        color: rgba(255, 255, 255, 0.9) !important;
    }

    /* 密码强度指示器样式 */
    .password-strength-wrapper {
        margin-top: 8px;
        margin-bottom: 16px;
    }

    .password-strength-bar {
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .password-strength-fill {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .password-strength-fill.weak {
        width: 33%;
        background: #ff4757;
    }

    .password-strength-fill.medium {
        width: 66%;
        background: #ffa502;
    }

    .password-strength-fill.strong {
        width: 100%;
        background: #2ed573;
    }

    .password-strength-text {
        font-size: 12px;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .password-strength-text.weak {
        color: #ff4757;
    }

    .password-strength-text.medium {
        color: #ffa502;
    }

    .password-strength-text.strong {
        color: #2ed573;
    }

    /* 页面切换动画 */
    #login-page, #register-page, #forgot-password-page {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    #login-page.fade-out, #register-page.fade-out, #forgot-password-page.fade-out {
        opacity: 0;
        transform: translateY(-20px);
    }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="login-page">
        <div class="login-container-new">
            <!-- 背景装饰元素 -->
            <div class="login-background-blur"></div>
            <div class="login-background-shapes">
                <div class="bg-shape bg-shape-1"></div>
                <div class="bg-shape bg-shape-2"></div>
            </div>
            
            <!-- 登录卡片 -->
            <div class="login-card-new">
                <div class="login-header-new">
                    <!-- Logo 区域 -->
                    <div class="login-logo-new">
                        <div class="logo-icon-container">
                            <i class="fas fa-bolt logo-icon-main"></i>
                        </div>
                        <div class="logo-text-container">
                            <img src="http://localhost:3845/assets/da821ed04dd0d1d5e21a4a63b731c1db13b08afc.svg" alt="TURING" class="logo-text-top">
                            <img src="http://localhost:3845/assets/b7e9417ad1d6832bd7efb3d4b0359e016e924363.svg" alt="MARKET" class="logo-text-bottom">
                        </div>
                    </div>
                </div>
                
                <div class="login-content-new">
                    <div class="login-title-section">
                        <h1 class="login-main-title">跨境运营助手</h1>
                        <p class="login-subtitle-new" id="login-subtitle">登录您的账号以继续</p>
                    </div>

                    <!-- 登录方式切换按钮 -->
                    <div class="login-mode-switch">
                        <button class="login-mode-switch-btn" id="login-mode-switch-btn">
                            <i class="fas fa-lock"></i>
                            <span>密码登录</span>
                        </button>
                    </div>

                    <div class="login-form-new">
                        <!-- 手机号输入 -->
                        <div class="login-input-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="tel" id="phone-input-new" placeholder="请输入手机号" class="login-input-new">
                        </div>

                        <!-- 验证码输入 -->
                        <div class="login-input-wrapper verification-wrapper" id="verification-wrapper">
                            <i class="fas fa-shield-alt input-icon-simple"></i>
                            <input type="text" id="verification-input-new" placeholder="请输入验证码" class="login-input-new verification-input" maxlength="6">
                            <button class="send-code-btn-new" id="send-code-btn-new">发送验证码</button>
                        </div>

                        <!-- 密码输入 -->
                        <div class="login-input-wrapper password-wrapper" id="password-wrapper" style="display: none;">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="password-input-new" placeholder="请输入密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 忘记密码链接 -->
                        <div class="forgot-password-wrapper" id="forgot-password-wrapper" style="display: none;">
                            <div style="text-align: right; margin-top: 8px;">
                                <a href="#" class="forgot-password-link" id="forgot-password-link" style="font-size: 13px; color: rgba(255, 255, 255, 0.6); text-decoration: none; transition: color 0.3s ease;">忘记密码？</a>
                            </div>
                        </div>

                        <!-- 登录按钮 -->
                        <button class="login-btn-new" id="login-btn-new">
                            登录
                        </button>
                        
                        <!-- Google 登录按钮 -->
                        <button class="google-login-btn-new" id="google-login-btn-new">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" class="google-icon">
                                <path fill="#FFC107" d="M43.6 20.2H24v7.5h11.3c-1.1 5.4-5.7 8.2-11.3 8.2-6.6 0-12-5.4-12-12s5.4-12 12-12c2.9 0 5.5 1 7.5 2.7l5.6-5.6C33.2 5.9 28.9 4 24 4 12.9 4 4 12.9 4 24s8.9 20 20 20c11 0 19.9-8 19.9-20 0-1.3-.2-2.7-.3-4z"/>
                                <path fill="#FF3D00" d="M6.3 14.7l6.5 4.8c1.4-4.2 5.3-7.2 9.8-7.2 2.9 0 5.5 1 7.5 2.7l5.6-5.6C31.8 5.9 28.2 4 24 4 16.8 4 10.9 8.7 6.3 14.7z"/>
                                <path fill="#4CAF50" d="M24 44c4.1 0 7.9-1.4 10.8-3.7l-5.6-4.3c-1.6 1.1-3.6 1.8-5.8 1.8-4.5 0-8.5-2.5-10.5-6.2l-6.5 5c4.5 8.9 13.7 14.4 24 14.4z"/>
                                <path fill="#1976D2" d="M43.6 20.2H24v7.5h11.3c-.5 2.6-1.9 4.9-3.9 6.4l5.6 4.3c3.3-3.1 5.4-7.6 5.4-12.7 0-1.3-.2-2.7-.3-4z"/>
                            </svg>
                            使用 Google 账号
                        </button>

                        <!-- 注册入口 -->
                        <div class="register-link-wrapper" id="register-link-wrapper">
                            <p class="register-text">还没有账号？ <a href="#" class="register-link" id="register-link">立即注册</a></p>
                        </div>

                    </div>
                    
                    <!-- 消息提示容器 -->
                    <div id="login-message-container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册页面 -->
    <div id="register-page" style="display: none;">
        <div class="login-container-new">
            <!-- 背景装饰元素 -->
            <div class="login-background-blur"></div>
            <div class="login-background-shapes">
                <div class="bg-shape bg-shape-1"></div>
                <div class="bg-shape bg-shape-2"></div>
            </div>

            <!-- 注册卡片 -->
            <div class="login-card-new">
                <div class="login-header-new">
                    <!-- Logo 区域 -->
                    <div class="login-logo-new">
                        <div class="logo-icon-container">
                            <i class="fas fa-bolt logo-icon-main"></i>
                        </div>
                        <div class="logo-text-container">
                            <img src="http://localhost:3845/assets/da821ed04dd0d1d5e21a4a63b731c1db13b08afc.svg" alt="TURING" class="logo-text-top">
                            <img src="http://localhost:3845/assets/b7e9417ad1d6832bd7efb3d4b0359e016e924363.svg" alt="MARKET" class="logo-text-bottom">
                        </div>
                    </div>
                </div>

                <div class="login-content-new">
                    <div class="login-title-section">
                        <h1 class="login-main-title">创建新账号</h1>
                        <p class="login-subtitle-new">注册您的账号以开始使用</p>
                    </div>

                    <div class="login-form-new">
                        <!-- 手机号输入 -->
                        <div class="login-input-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="tel" id="register-phone-input" placeholder="请输入手机号" class="login-input-new">
                        </div>

                        <!-- 验证码输入 -->
                        <div class="login-input-wrapper verification-wrapper">
                            <i class="fas fa-shield-alt input-icon-simple"></i>
                            <input type="text" id="register-verification-input" placeholder="请输入验证码" class="login-input-new verification-input" maxlength="6">
                            <button class="send-code-btn-new" id="register-send-code-btn">发送验证码</button>
                        </div>

                        <!-- 密码输入 -->
                        <div class="login-input-wrapper password-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="register-password-input" placeholder="请设置密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="register-password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 密码强度指示器 -->
                        <div class="password-strength-wrapper" id="register-password-strength-wrapper">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill" id="register-password-strength-fill"></div>
                            </div>
                            <div class="password-strength-text" id="register-password-strength-text">密码强度：弱</div>
                        </div>

                        <!-- 确认密码输入 -->
                        <div class="login-input-wrapper password-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="register-confirm-password-input" placeholder="请确认密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="register-confirm-password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 注册按钮 -->
                        <button class="login-btn-new" id="register-btn">
                            注册
                        </button>

                        <!-- 返回登录 -->
                        <div class="register-link-wrapper">
                            <p class="register-text">已有账号？ <a href="#" class="register-link" id="back-to-login-link">立即登录</a></p>
                        </div>

                    </div>
                    
                    <!-- 消息提示容器 -->
                    <div id="register-message-container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 忘记密码页面 -->
    <div id="forgot-password-page" style="display: none;">
        <div class="login-container-new">
            <!-- 背景装饰元素 -->
            <div class="login-background-blur"></div>
            <div class="login-background-shapes">
                <div class="bg-shape bg-shape-1"></div>
                <div class="bg-shape bg-shape-2"></div>
            </div>

            <!-- 忘记密码卡片 -->
            <div class="login-card-new">
                <div class="login-header-new">
                    <!-- Logo 区域 -->
                    <div class="login-logo-new">
                        <div class="logo-icon-container">
                            <i class="fas fa-bolt logo-icon-main"></i>
                        </div>
                        <div class="logo-text-container">
                            <img src="http://localhost:3845/assets/da821ed04dd0d1d5e21a4a63b731c1db13b08afc.svg" alt="TURING" class="logo-text-top">
                            <img src="http://localhost:3845/assets/b7e9417ad1d6832bd7efb3d4b0359e016e924363.svg" alt="MARKET" class="logo-text-bottom">
                        </div>
                    </div>
                </div>

                <div class="login-content-new">
                    <div class="login-title-section">
                        <h1 class="login-main-title">重置密码</h1>
                        <p class="login-subtitle-new">通过手机验证码重置您的登录密码</p>
                    </div>

                    <div class="login-form-new">
                        <!-- 手机号输入 -->
                        <div class="login-input-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="tel" id="reset-phone-input" placeholder="请输入手机号" class="login-input-new">
                        </div>

                        <!-- 验证码输入 -->
                        <div class="login-input-wrapper verification-wrapper">
                            <i class="fas fa-shield-alt input-icon-simple"></i>
                            <input type="text" id="reset-verification-input" placeholder="请输入验证码" class="login-input-new verification-input" maxlength="6">
                            <button class="send-code-btn-new" id="reset-send-code-btn">发送验证码</button>
                        </div>

                        <!-- 新密码输入 -->
                        <div class="login-input-wrapper password-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="reset-password-input" placeholder="请设置新密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="reset-password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 密码强度指示器 -->
                        <div class="password-strength-wrapper" id="reset-password-strength-wrapper">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill" id="reset-password-strength-fill"></div>
                            </div>
                            <div class="password-strength-text" id="reset-password-strength-text">密码强度：弱</div>
                        </div>

                        <!-- 确认密码输入 -->
                        <div class="login-input-wrapper password-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="reset-confirm-password-input" placeholder="请确认新密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="reset-confirm-password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 重置密码按钮 -->
                        <button class="login-btn-new" id="reset-password-btn">
                            重置密码
                        </button>

                        <!-- 返回登录 -->
                        <div class="register-link-wrapper">
                            <p class="register-text">想起密码了？ <a href="#" class="register-link" id="back-to-login-from-reset">返回登录</a></p>
                        </div>

                    </div>
                    
                    <!-- 消息提示容器 -->
                    <div id="reset-message-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 依赖的 JS 文件 (注意路径调整) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="../themes/theme-config.js"></script>
    <script src="../themes/theme-manager.js"></script>
    <script src="script.js"></script>
    <script>
        // 页面加载完成后，显式地初始化登录页面逻辑
        document.addEventListener('DOMContentLoaded', () => {
            if (typeof setupLoginPage === 'function') {
                setupLoginPage();
            } else {
                console.error('Login script not loaded or setupLoginPage function is missing.');
            }
        });
    </script>
</body>
</html> 
