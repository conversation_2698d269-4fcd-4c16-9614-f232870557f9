// ===============================================
// == 登录、注册、密码重置页面专属脚本 ==
// ===============================================

document.addEventListener('DOMContentLoaded', () => {
    console.log('🔒 Auth script loaded');

    // 尝试获取所有需要的元素
    const loginPage = document.getElementById('login-page');
    const registerPage = document.getElementById('register-page');
    const forgotPasswordPage = document.getElementById('forgot-password-page');
    
    // 由于这是登录页专属脚本，我们期望 loginPage 存在
    if (loginPage) {
        setupLoginPage();
    } else if (registerPage) {
        setupRegisterPage();
    } else if (forgotPasswordPage) {
        setupForgotPasswordPage();
    } else {
        console.error('无法找到登录、注册或忘记密码页面容器。');
    }
});

/**
 * 设置注册页面功能
 */
function setupRegisterPage() {
    console.log('🚀 Initializing register page...');
    initRegisterForm();
}

/**
 * 设置忘记密码页面功能
 */
function setupForgotPasswordPage() {
    console.log('🚀 Initializing forgot password page...');
    initForgotPasswordForm();
}


/**
 * 设置登录和注册页面的核心功能
 */
function setupLoginPage() {
    console.log('🚀 Initializing login page...');

    // 页面切换逻辑
    const registerLink = document.getElementById('register-link');
    const backToLoginLink = document.getElementById('back-to-login-link');
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    const backToLoginFromReset = document.getElementById('back-to-login-from-reset');

    if (registerLink) {
        registerLink.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('register-page');
        });
    }

    if (backToLoginLink) {
        backToLoginLink.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('login-page');
        });
    }

    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('forgot-password-page');
        });
    }

    if (backToLoginFromReset) {
        backToLoginFromReset.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('login-page');
        });
    }

    // 初始化选项卡切换功能
    initTabSwitching();
    // 初始化登录表单功能
    initLoginForm();
    // 初始化注册表单功能
    initRegisterForm();
    // 初始化忘记密码表单功能
    initForgotPasswordForm();

    // 默认显示登录页面
    showPage('login-page');
}


/**
 * 显示指定的页面
 * @param {string} pageId 
 */
function showPage(pageId) {
    const pages = ['login-page', 'register-page', 'forgot-password-page'];
    
    pages.forEach(id => {
        const page = document.getElementById(id);
        if (page) {
            if (id === pageId) {
                page.style.display = 'block';
                page.classList.remove('fade-out');
            } else {
                page.classList.add('fade-out');
                setTimeout(() => {
                    page.style.display = 'none';
                }, 300);
            }
        }
    });
}


/**
 * 初始化选项卡切换功能
 */
function initTabSwitching() {
    const tabs = document.querySelectorAll('.tab-item');

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTab = tab.dataset.tab;
            switchTab(targetTab);
        });
    });

    // 默认显示短信登录选项卡
    switchTab('sms');
}

/**
 * 切换选项卡
 */
function switchTab(tabName) {
    const tabs = document.querySelectorAll('.tab-item');
    const contents = document.querySelectorAll('.tab-content');

    // 更新选项卡状态
    tabs.forEach(tab => {
        if (tab.dataset.tab === tabName) {
            tab.classList.add('active');
        } else {
            tab.classList.remove('active');
        }
    });

    // 更新内容区域
    contents.forEach(content => {
        if (content.id === `${tabName}-login-content`) {
            content.classList.add('active');
        } else {
            content.classList.remove('active');
        }
    });
}

/**
 * 初始化登录表单
 */
function initLoginForm() {
    // 短信登录相关元素
    const smsLoginBtn = document.getElementById('sms-login-btn');
    const smsSendCodeBtn = document.getElementById('sms-send-code-btn');
    const smsPhoneInput = document.getElementById('sms-phone-input');

    // 密码登录相关元素
    const passwordLoginBtn = document.getElementById('password-login-btn');
    const passwordRegisterBtn = document.getElementById('password-register-btn');
    const forgotPasswordLink = document.getElementById('forgot-password-link');

    // Google登录按钮
    const googleLoginBtn = document.getElementById('google-login-btn-new');

    // 密码可见性切换
    initPasswordToggle('password-toggle-btn', 'password-input-new');

    // 短信登录事件
    if (smsLoginBtn) {
        smsLoginBtn.addEventListener('click', () => handleSmsLogin());
    }

    if (smsSendCodeBtn) {
        smsSendCodeBtn.addEventListener('click', () => {
            handleSendVerificationCode(smsPhoneInput.value, smsSendCodeBtn, 'sms');
        });
    }

    // 密码登录事件
    if (passwordLoginBtn) {
        passwordLoginBtn.addEventListener('click', () => handlePasswordLogin());
    }

    if (passwordRegisterBtn) {
        passwordRegisterBtn.addEventListener('click', () => {
            showPage('register-page');
        });
    }

    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('forgot-password-page');
        });
    }

    // Google登录事件
    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', () => {
            showLoginMessage('正在跳转到 Google 登录...', 'info');
            setTimeout(() => {
                alert('与Google OAuth集成。');
            }, 1000);
        });
    }
}

/**
 * 初始化注册表单
 */
function initRegisterForm() {
    const registerBtn = document.getElementById('register-btn');
    const sendCodeBtn = document.getElementById('register-send-code-btn');
    const phoneInput = document.getElementById('register-phone-input');
    const usernameInput = document.getElementById('register-username-input');
    const passwordInput = document.getElementById('register-password-input');

    initPasswordToggle('register-password-toggle-btn', 'register-password-input');
    initPasswordToggle('register-confirm-password-toggle-btn', 'register-confirm-password-input');

    if(registerBtn) {
        registerBtn.addEventListener('click', () => handleRegister());
    }

    if(sendCodeBtn) {
        sendCodeBtn.addEventListener('click', () => {
            handleSendVerificationCode(phoneInput.value, sendCodeBtn, 'register');
        });
    }

    if(passwordInput) {
        passwordInput.addEventListener('input', () => {
             checkPasswordStrength(passwordInput.value, 'register');
        });
    }

    if(usernameInput) {
        usernameInput.addEventListener('input', () => {
            checkUsernameAvailability(usernameInput.value);
        });
    }
}

/**
 * 处理短信登录/注册逻辑
 */
function handleSmsLogin() {
    const phoneInput = document.getElementById('sms-phone-input');
    const verificationInput = document.getElementById('sms-verification-input');

    const phone = phoneInput.value;
    const code = verificationInput.value;

    if (!phone || !code) {
        showLoginMessage('手机号和验证码不能为空', 'error');
        return;
    }

    if (!isValidPhoneNumber(phone)) {
        showLoginMessage('请输入有效的手机号', 'error');
        return;
    }

    showLoginMessage(`正在使用验证码为 ${phone} 登录/注册...`, 'info');

    // 模拟API调用 - 统一登录/注册流程
    setTimeout(() => {
        // 模拟判断是新用户还是老用户
        const isNewUser = Math.random() > 0.5; // 随机模拟

        if (isNewUser) {
            showLoginMessage('检测到新用户，正在为您自动注册并登录...', 'success');
        } else {
            showLoginMessage('登录成功！正在跳转...', 'success');
        }

        localStorage.setItem('userLoggedIn', 'true');
        setTimeout(() => {
            window.location.href = '../index.html';
        }, 1500);
    }, 1000);
}

/**
 * 处理密码登录逻辑
 */
function handlePasswordLogin() {
    const accountInput = document.getElementById('password-account-input');
    const passwordInput = document.getElementById('password-input-new');

    const account = accountInput.value;
    const password = passwordInput.value;

    if (!account || !password) {
        showLoginMessage('账号和密码不能为空', 'error');
        return;
    }

    showLoginMessage(`正在使用密码为账号 ${account} 登录...`, 'info');

    // 模拟API调用
    setTimeout(() => {
        // 模拟登录验证
        if (account === 'demo' && password === '********') {
            showLoginMessage('登录成功！正在跳转...', 'success');
            localStorage.setItem('userLoggedIn', 'true');
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 1500);
        } else {
            showLoginMessage('账号或密码错误，请检查后重试', 'error');
        }
    }, 1000);
}

/**
 * 处理注册逻辑
 */
function handleRegister() {
    const phone = document.getElementById('register-phone-input').value;
    const code = document.getElementById('register-verification-input').value;
    const username = document.getElementById('register-username-input').value;
    const password = document.getElementById('register-password-input').value;
    const confirmPassword = document.getElementById('register-confirm-password-input').value;

    if (!phone || !code || !username || !password || !confirmPassword) {
        showRegisterMessage("请填写所有字段", 'error');
        return;
    }

    if (!isValidPhoneNumber(phone)) {
        showRegisterMessage("请输入有效的手机号", 'error');
        return;
    }

    if (!isValidUsername(username)) {
        showRegisterMessage("账号必须为3-20位字符，支持字母、数字、下划线，不能以数字开头", 'error');
        return;
    }

    if (!isValidPassword(password)) {
        showRegisterMessage("密码必须为8-20位，包含字母和数字", 'error');
        return;
    }

    if (password !== confirmPassword) {
        showRegisterMessage("两次输入的密码不一致", 'error');
        return;
    }

    showRegisterMessage(`正在为手机号 ${phone} 创建账号 ${username}...`, 'info');
    // 模拟API调用
    setTimeout(() => {
        showRegisterMessage('注册成功！正在为您登录...', 'success');
        localStorage.setItem('userLoggedIn', 'true');
        localStorage.setItem('username', username);
        setTimeout(() => {
            window.location.href = '../index.html';
        }, 1500);
    }, 1000);
}



/**
 * 初始化密码可见性切换按钮
 * @param {string} btnId 
 * @param {string} inputId 
 */
function initPasswordToggle(btnId, inputId) {
    const toggleBtn = document.getElementById(btnId);
    const passwordInput = document.getElementById(inputId);

    if (toggleBtn && passwordInput) {
        toggleBtn.addEventListener('click', () => {
            togglePasswordVisibility(passwordInput, toggleBtn);
        });
    }
}

/**
 * 切换密码的可见性
 * @param {HTMLInputElement} input 
 * @param {HTMLButtonElement} button 
 */
function togglePasswordVisibility(input, button) {
    const icon = button.querySelector('i');
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'ri-eye-line';
    } else {
        input.type = 'password';
        icon.className = 'ri-eye-off-line';
    }
}

/**
 * 检查密码强度
 * @param {string} password 
 * @param {string} prefix 'register' or 'reset'
 */
function checkPasswordStrength(password, prefix = 'register') {
    const strength = getPasswordStrength(password);
    const strengthBar = document.getElementById(`${prefix}-password-strength-fill`);
    const strengthText = document.getElementById(`${prefix}-password-strength-text`);

    if (!strengthBar || !strengthText) return;

    strengthBar.className = `password-strength-fill ${strength.level}`;
    strengthText.textContent = `密码强度：${strength.text}`;
    strengthText.className = `password-strength-text ${strength.level}`;
}

/**
 * 获取密码强度
 * @param {string} password 
 * @returns {{level: string, text: string}}
 */
function getPasswordStrength(password) {
    let score = 0;
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[^a-zA-Z0-9]/.test(password)) score++;

    if (score < 3) return { level: 'weak', text: '弱' };
    if (score < 4) return { level: 'medium', text: '中等' };
    return { level: 'strong', text: '强' };
}

/**
 * 处理发送验证码
 * @param {string} phone
 * @param {HTMLButtonElement} button
 * @param {string} prefix
 */
function handleSendVerificationCode(phone, button, prefix) {
    if (!isValidPhoneNumber(phone)) {
        if (prefix === 'sms') showLoginMessage('请输入有效的手机号', 'error');
        else if (prefix === 'register') showRegisterMessage('请输入有效的手机号', 'error');
        else if (prefix === 'reset') showResetMessage('请输入有效的手机号', 'error');
        return;
    }

    if (prefix === 'sms') showLoginMessage(`验证码已发送至 ${phone}`, 'success');
    else if (prefix === 'register') showRegisterMessage(`验证码已发送至 ${phone}`, 'success');
    else if (prefix === 'reset') showResetMessage(`验证码已发送至 ${phone}`, 'success');

    startCountdown(button);
}

/**
 * 验证手机号格式
 * @param {string} phone
 * @returns {boolean}
 */
function isValidPhoneNumber(phone) {
    // 简单验证，实际应用中应使用更复杂的正则表达式
    return /^1[3-9]\d{9}$/.test(phone);
}

/**
 * 验证用户名格式
 * @param {string} username
 * @returns {boolean}
 */
function isValidUsername(username) {
    // 长度：3-20位字符
    if (username.length < 3 || username.length > 20) return false;

    // 支持字符：字母、数字、下划线，不能以数字开头
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(username)) return false;

    return true;
}

/**
 * 检查用户名可用性
 * @param {string} username
 */
function checkUsernameAvailability(username) {
    const statusElement = document.getElementById('username-status');
    const messageElement = document.getElementById('username-status-message');

    if (!statusElement || !messageElement) return;

    if (!username) {
        statusElement.style.display = 'none';
        return;
    }

    if (!isValidUsername(username)) {
        statusElement.style.display = 'block';
        statusElement.className = 'username-status unavailable';
        messageElement.textContent = '账号格式不正确（3-20位字符，支持字母、数字、下划线，不能以数字开头）';
        return;
    }

    // 显示检查中状态
    statusElement.style.display = 'block';
    statusElement.className = 'username-status checking';
    messageElement.textContent = '正在检查账号可用性...';

    // 模拟API检查
    setTimeout(() => {
        // 模拟一些已占用的用户名
        const unavailableUsernames = ['admin', 'root', 'test', 'user', 'demo'];
        const isAvailable = !unavailableUsernames.includes(username.toLowerCase());

        if (isAvailable) {
            statusElement.className = 'username-status available';
            messageElement.textContent = '账号可用';
        } else {
            statusElement.className = 'username-status unavailable';
            messageElement.textContent = '账号已被占用，请尝试其他账号';
        }
    }, 800);
}


/**
 * 开始发送验证码按钮的倒计时
 * @param {HTMLButtonElement} button 
 * @param {number} seconds 
 */
function startCountdown(button, seconds = 60) {
    button.disabled = true;
    let timer = seconds;
    button.textContent = `${timer}s 后重发`;

    const interval = setInterval(() => {
        timer--;
        if (timer > 0) {
            button.textContent = `${timer}s 后重发`;
        } else {
            clearInterval(interval);
            button.disabled = false;
            button.textContent = '发送验证码';
        }
    }, 1000);
}


/**
 * 在登录页显示消息
 * @param {string} message 
 * @param {'info' | 'success' | 'error'} type 
 */
function showLoginMessage(message, type = 'info') {
    const container = document.getElementById('login-message-container');
    if (!container) return;
    
    const messageElement = document.createElement('div');
    messageElement.className = `login-message ${type}`;
    messageElement.textContent = message;
    
    container.innerHTML = '';
    container.appendChild(messageElement);

    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => messageElement.remove(), 500);
    }, 3000);
}

/**
 * 在注册页显示消息
 * @param {string} message 
 * @param {'info' | 'success' | 'error'} type 
 */
function showRegisterMessage(message, type = 'info') {
    const container = document.getElementById('register-message-container');
     if (!container) {
        // Fallback to login message container if not on register page
        showLoginMessage(message, type);
        return;
    }
    
    const messageElement = document.createElement('div');
    messageElement.className = `login-message ${type}`;
    messageElement.textContent = message;
    
    container.innerHTML = '';
    container.appendChild(messageElement);

    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => messageElement.remove(), 500);
    }, 3000);
}



/**
 * 初始化忘记密码表单
 */
function initForgotPasswordForm() {
    const findPhoneBtn = document.getElementById('find-phone-btn');
    const sendCodeBtn = document.getElementById('reset-send-code-btn');
    const verifyCodeBtn = document.getElementById('verify-code-btn');
    const resetBtn = document.getElementById('reset-password-btn');
    const passwordInput = document.getElementById('reset-password-input');

    initPasswordToggle('reset-password-toggle-btn', 'reset-password-input');
    initPasswordToggle('reset-confirm-password-toggle-btn', 'reset-confirm-password-input');

    if (findPhoneBtn) {
        findPhoneBtn.addEventListener('click', () => handleFindPhone());
    }

    if (sendCodeBtn) {
        sendCodeBtn.addEventListener('click', () => handleSendResetCode());
    }

    if (verifyCodeBtn) {
        verifyCodeBtn.addEventListener('click', () => handleVerifyResetCode());
    }

    if (resetBtn) {
        resetBtn.addEventListener('click', () => handlePasswordReset());
    }

    if (passwordInput) {
        passwordInput.addEventListener('input', () => {
            checkPasswordStrength(passwordInput.value, 'reset');
        });
    }

    // 默认显示第一步
    showResetStep(1);
}

/**
 * 显示重置密码的指定步骤
 * @param {number} step
 */
function showResetStep(step) {
    const steps = document.querySelectorAll('.reset-step');
    const subtitle = document.getElementById('reset-subtitle');

    steps.forEach((stepElement, index) => {
        if (index + 1 === step) {
            stepElement.classList.add('active');
        } else {
            stepElement.classList.remove('active');
        }
    });

    // 更新副标题
    const subtitles = [
        '通过账号查找绑定手机号重置密码',
        '验证绑定的手机号',
        '设置新的登录密码'
    ];

    if (subtitle && subtitles[step - 1]) {
        subtitle.textContent = subtitles[step - 1];
    }
}

/**
 * 处理查找绑定手机号
 */
function handleFindPhone() {
    const accountInput = document.getElementById('reset-account-input');
    const account = accountInput.value;

    if (!account) {
        showResetMessage('请输入您的账号', 'error');
        return;
    }

    showResetMessage('正在查找绑定的手机号...', 'info');

    // 模拟API调用查找绑定手机号
    setTimeout(() => {
        // 模拟一些存在的账号
        const existingAccounts = {
            'demo': '138****8888',
            'test': '139****9999',
            'user': '136****6666'
        };

        const maskedPhone = existingAccounts[account.toLowerCase()];

        if (maskedPhone) {
            showResetMessage('找到绑定手机号！', 'success');

            // 显示绑定的手机号
            const maskedPhoneElement = document.getElementById('masked-phone');
            if (maskedPhoneElement) {
                maskedPhoneElement.textContent = maskedPhone;
            }

            // 进入第二步
            setTimeout(() => {
                showResetStep(2);
            }, 1000);
        } else {
            showResetMessage('未找到该账号，请检查账号是否正确', 'error');
        }
    }, 1000);
}

/**
 * 处理发送重置验证码
 */
function handleSendResetCode() {
    const sendCodeBtn = document.getElementById('reset-send-code-btn');
    showResetMessage('验证码已发送，请查收', 'success');
    startCountdown(sendCodeBtn);
}

/**
 * 处理验证重置验证码
 */
function handleVerifyResetCode() {
    const codeInput = document.getElementById('reset-verification-input');
    const code = codeInput.value;

    if (!code) {
        showResetMessage('请输入验证码', 'error');
        return;
    }

    showResetMessage('正在验证验证码...', 'info');

    // 模拟验证码验证
    setTimeout(() => {
        showResetMessage('验证码验证成功！', 'success');
        setTimeout(() => {
            showResetStep(3);
        }, 1000);
    }, 1000);
}

/**
 * 处理密码重置逻辑
 */
function handlePasswordReset() {
    const password = document.getElementById('reset-password-input').value;
    const confirmPassword = document.getElementById('reset-confirm-password-input').value;

    if (!password || !confirmPassword) {
        showResetMessage("请填写所有字段", 'error');
        return;
    }

    if (!isValidPassword(password)) {
        showResetMessage("密码必须为8-20位，包含字母和数字", 'error');
        return;
    }

    if (password !== confirmPassword) {
        showResetMessage("两次输入的密码不一致", 'error');
        return;
    }

    showResetMessage('正在重置密码...', 'info');
    // 模拟API调用
    setTimeout(() => {
        showResetMessage('密码重置成功！正在跳转到登录页面...', 'success');
        setTimeout(() => {
            showPage('login-page');
            // 重置表单状态
            showResetStep(1);
        }, 1500);
    }, 1000);
}

/**
 * 验证密码格式
 * @param {string} password 
 * @returns {boolean}
 */
function isValidPassword(password) {
    // 8-20位，至少包含字母和数字
    if (password.length < 8 || password.length > 20) return false;
    if (!/[a-zA-Z]/.test(password)) return false;
    if (!/\d/.test(password)) return false;
    return true;
}

/**
 * 在重置密码页显示消息
 * @param {string} message 
 * @param {'info' | 'success' | 'error'} type 
 */
function showResetMessage(message, type = 'info') {
    const container = document.getElementById('reset-message-container');
    if (!container) {
        showLoginMessage(message, type);
        return;
    }
    
    const messageElement = document.createElement('div');
    messageElement.className = `login-message ${type}`;
    messageElement.textContent = message;
    
    container.innerHTML = '';
    container.appendChild(messageElement);

    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => messageElement.remove(), 500);
    }, 3000);
}
